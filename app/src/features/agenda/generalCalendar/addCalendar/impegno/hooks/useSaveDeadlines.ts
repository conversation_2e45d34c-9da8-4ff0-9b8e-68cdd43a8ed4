import { useState } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { IDeadlineParams } from "../interfaces/impegno.interface";

export default function useSaveDeadlines(postUrl?: string) {
    const saveDeadlineRequest = usePostCustom(
        `${postUrl ? postUrl : "deadlines"}/save?noTemplateVars=true`
    );

    const overlappingDeadlinesRequest = usePostCustom(
        "deadlines/overlapping-deadlines?noTemplateVars=true"
    );

    const [requiredFields, setRequiredFields] = useState({
        deadlineText: false,
        deadlinePeriod: false,
        deadlineUser: false,
        deadlineType: false,
    });

    const saveDeadline = async (
        deadlineSaveParams: any,
        iscreatedFromDeadline: boolean = false,
        controlOverlapping: boolean = true
    ) => {
        const newRequiredFields = {
            deadlineText: deadlineSaveParams.deadlineText === "",
            deadlinePeriod: false, // keep your logic for this below
            deadlineUser:
                !deadlineSaveParams.deadlineUser ||
                (Array.isArray(deadlineSaveParams.deadlineUser) &&
                    deadlineSaveParams.deadlineUser.length === 0),
            deadlineType:
                deadlineSaveParams.deadlineType === undefined ||
                deadlineSaveParams.deadlineType === null ||
                deadlineSaveParams.deadlineType === "",
        };

        // Handle deadlinePeriod required only if iscreatedFromDeadline is true
        if (deadlineSaveParams.deadlinePeriod === "0" && iscreatedFromDeadline) {
            newRequiredFields.deadlinePeriod = true;
        }

        setRequiredFields(newRequiredFields);

        if (
            newRequiredFields.deadlineText ||
            newRequiredFields.deadlinePeriod ||
            newRequiredFields.deadlineUser ||
            newRequiredFields.deadlineType
        ) {
            return;
        }

        const formData = new FormData();

        const deadlineUser: any = [];
        Object.keys(deadlineSaveParams).forEach((key) => {
            const value = deadlineSaveParams[key as keyof IDeadlineParams];

            if (value === false) return;

            if (key === "deadlineUser" && Array.isArray(value)) {
                value.forEach((user: any) => {
                    formData.append("deadlineUser[]", user.id);
                    deadlineUser.push(user.id);
                });
                const userIds = value.map((user: any) => user.id).join(",");
                formData.append("deadlineUserData", userIds);
                return;
            }

            if (key === "deadLinesGroups" && value?.id) {
                formData.append("deadLinesGroups", value.id);
                return;
            }

            formData.append(key, value);
        });

        if (controlOverlapping) {
            const responseOverlapping: any =
                await overlappingDeadlinesRequest.doFetch(
                    true,
                    { ...deadlineSaveParams, deadlineUser },
                    "post",
                    "json",
                    true
                );

            if (responseOverlapping.data.length > 0) {
                return {
                    overlappingDeadlines: true,
                    data: responseOverlapping.data,
                };
            }
        }

        formData.delete("deadlineLinkud");

        const response: any = await saveDeadlineRequest.doFetch(true, formData);
        return response.data;
    };

    return {
        saveDeadline,
        requiredFields,
        setRequiredFields,
    };
}
