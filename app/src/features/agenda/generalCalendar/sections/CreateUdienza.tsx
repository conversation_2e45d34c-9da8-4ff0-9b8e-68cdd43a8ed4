import React, { useState } from "react";
import { Ta<PERSON>, Tab } from "@vapor/react-extended";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { SchedaUdienzaTab } from "./SchedaUdienzaTab";
import { Authority } from "./Authority";
import { Box, TextField, CircularProgress, TableContainer, TableBody, TableRow } from "@vapor/react-material";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { debounce } from "lodash";
import { useLocation } from "react-router-dom";
import DateAggiuntivi from "./DatiAggiuntivi";
import { useNavigate } from "react-router-dom";
import { useAgendaFormHook } from "../hooks/useAgendaFormHook";
import { useAgendaHooks } from "../hooks/useAgendaHooks";
import { AgendaUpdateProvider } from "../providers/AgendaUpdateProvider";
import moment from "moment";
import { removeLinks } from "../../../../utilities/utils";
import { useAgendaParams } from "../../hooks/useAgendaParams.hook";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";
interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
            {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`
    };
}

export const removeBoldTags = (text: string) => {
    return text.replace(/<\/?b>/g, "");
};

type Validation = "default" | "timesheet";

const CreateUdienza = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [, setDeadlineLinkuId] = React.useState("");
    const [overlappingModal, setOverlappingModal] = useState(false);
    const [conflictsMessage, setConflictsMessage] = useState<string>("");
    const [selectedAfterAuthority, setSelectedAfterAuthority] = useState("");
    const [pendingFormData, setPendingFormData] = useState<any>(null);
    const [rowData, setRowData] = useState<{
        form: any;
        users: any[];
        isLastHearing: boolean;
    }>({
        form: {},
        users: [],
        isLastHearing: false
    });

    const [validation, setValidation] = useState<Validation>("default");
    const method: any = useAgendaFormHook(validation);

    const { items } = useAgendaHooks();
    const { reset } = method;

    const [selectedPractica, setSelectedPractica] = React.useState({
        value: "",
        label: "",
        fileUniqueid: ""
    });
    const [isLoading, setIsLoading] = useState(false);
    const [search, setSearch] = React.useState("");
    const [practicaArchive, setPracticaArchive] = React.useState([]);
    const [showFullForm, setShowFullForm] = useState(false);
    const [showAuthorityForm, setShowAuthorityForm] = useState(false);
    const [value, setValue] = React.useState(0);

    const locationState = useAgendaParams();
    const { paramId } = locationState;

    React.useEffect(() => {
        const fetchRowData = async () => {
            const hasAuthority = await checkAuthorita();

            if (!hasAuthority) {
                setShowAuthorityForm(true);
                setShowFullForm(false);
                return;
            }

            const response: any = await getRowDataRequest.doFetch(true, {
                fileUniqueid: locationState.fileUniqueid,
                uniqueId: ""
            });

            setRowData(response.data);
            setShowFullForm(true);
        };

        if (locationState.fileHeaderData?.id) {
            setSelectedPractica({
                value: locationState.fileHeaderData?.id,
                label: locationState.fileHeaderData?.headerArchive,
                fileUniqueid: locationState?.fileUniqueid || ""
            });

            fetchRowData();
        }
    }, [locationState?.fileHeaderData?.id]);

    React.useEffect(() => {
        if (selectedAfterAuthority) {
            getRowData({
                fileUniqueid: selectedAfterAuthority,
                uniqueId: ""
            });
        }
    }, [selectedAfterAuthority]);

    React.useEffect(() => {
        if (locationState?.rowData?.form?.agendaArchiveuid) {
            setShowFullForm(true);
            setRowData(locationState.rowData);

            const form = locationState?.rowData.form;
            form.referente = [{ id: form.avvocato }];
            reset(form);
        }
    }, [locationState?.rowData?.form?.agendaArchiveuid]);

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };
    const usePostRequest = usePostCustom(`archiveagenda/save?noTemplateVars=true`);

    const authoritaCheckRequest = usePostCustom("archiveagenda/checkfileauthority?noTemplateVars=true");

    const getRowDataRequest = usePostCustom(`${locationState.rowDataUrl}/getrowdata?noTemplateVars=true`);

    const getArchivePracticaDataReq = useGetCustom(search ? `default/archive/search?from=calendar&noTemplateVars=true&q=${search}` : "default/archive/search?from=calendar&noTemplateVars=true");

    const overlappingDeadlinesCheck = usePostCustom(`deadlines/overlapping-deadlines?noTemplateVars=true`);

    const getPracticaArchiveData = React.useCallback(async () => {
        setIsLoading(true);
        try {
            const response: any = await getArchivePracticaDataReq.doFetch(true);
            setPracticaArchive(
                response.data.map((item: any) => {
                    const label = removeLinks(item.headerArchive ? item.headerArchive : `${item.listaclienti || ""} contro ${item.listacontroparti || ""}`, " ");

                    return {
                        value: item.uniqueid,
                        label
                    };
                })
            );
        } finally {
            setIsLoading(false);
        }
    }, [search]);

    const debouncedGetPracticaArchiveData = React.useCallback(debounce(getPracticaArchiveData, 500), [getPracticaArchiveData]);

    React.useEffect(() => {
        debouncedGetPracticaArchiveData();

        return () => {
            debouncedGetPracticaArchiveData.cancel();
        };
    }, [search, debouncedGetPracticaArchiveData]);

    const checkAuthorita = async (query?: any): Promise<boolean> => {
        const response: any = await authoritaCheckRequest.doFetch(
            true,
            query
                ? query
                : {
                      fileUniqueid: locationState.fileUniqueid,
                      uniqueId: ""
                  }
        );
        return !!response.data;
    };

    const getRowData = async (query: any) => {
        let { data }: any = await getRowDataRequest.doFetch(true, query);

        data.origin = "agenda";
        data.type = "update";
        data.items = locationState.prevPath === "/calendar/calendar" ? items : locationState.items;
        navigate(`/archiveagenda/agenda/create/${data.form.agendaArchiveuid}`, {
            state: {
                rowData: data,
                prevPath: locationState.prevPath,
                defaultParams: locationState.defaultParams
            }
        });
        console.log({ data });
        setDeadlineLinkuId(data.form.deadlineLinkuid);
    };

    console.log({ rowData: rowData.form.agendaEvasa });

    const handlePraticaChange = async (item: any) => {
        setSelectedPractica(item);
        const query = {
            fileUniqueid: item.value,
            uniqueId: ""
        };

        if (item) {
            const hasAuthority = await checkAuthorita(query);

            if (hasAuthority) {
                setShowFullForm(true);
                setShowAuthorityForm(false);
                getRowData(query);
            } else {
                setShowAuthorityForm(true);
            }
        } else {
            setShowFullForm(false);
        }
    };

    const { t } = useTranslation();

    const onSubmit = async (data: any) => {
        const dataCopy = JSON.parse(JSON.stringify(data));

        const formatDate = (isoString: string) => {
            const date = new Date(isoString);
            return date.toLocaleDateString("it-IT");
        };

        const formData = new FormData();
        const hourAndMinutes = data.ora ? moment(data.ora).format("HH:mm").split(":") : ["09", "00"];

        formData.append("uniqueid", data?.agendaUniqueid ?? "");
        formData.append("fkUniqueIdUdienzaReinvio", data?.agendaUniqueid ?? "");
        formData.append("archiveuid", locationState.fileUniqueid || paramId || "");
        formData.append("data", formatDate(data.date));
        formData.append("ora", hourAndMinutes[0]);
        formData.append("minuti", hourAndMinutes[1]);
        formData.append("agendaDaysBefore", data.agendaDaysBefore || "");
        formData.append("agendaPeriod", data.agendaPeriod || "");
        data.referente.forEach((ref: any) => formData.append("referente[]", ref.hasOwnProperty("id") ? ref.id : ref.value));
        formData.append("attivita", data.attivita);
        formData.append("sezione", data.agendaSezione);
        formData.append("autorita", data.agendaAutorita);
        formData.append("citta", data.agendaCitta);
        formData.append("istruttore", data.agendaIstruttore);
        formData.append("annotazioni", data.agendaAnnotazioni);
        if (data.agendaBillable === "true") formData.append("agendaBillable", data.agendaBillable);
        if (data.agendaEvasa === "true") formData.append("agendaEvasa", data.agendaEvasa);
        formData.append("statopratica", data.agendaStatopratica);
        formData.append("parentItemId", data.parentItemId || "");

        formData.append("referenteData", data.referente.map((ref: any) => (ref.hasOwnProperty("id") ? ref.id : ref.value)).join(","));

        const overlappingDeadlinesData = {
            deadlineDate: formatDate(data.date),
            deadlineHours: hourAndMinutes[0],
            deadlineMinutes: hourAndMinutes[1],
            deadlinePeriod: data.agendaPeriod,
            deadlineUniqueid: data.agendaUniqueid || "",
            deadlineUser: data.referente
                .map((ref: any) => {
                    const id = ref?.id ?? ref?.value;
                    return id !== undefined ? String(id) : "";
                })
                .filter((id: string) => id !== "")
        };

        const overlappingDeadlines: any = await overlappingDeadlinesCheck.doFetch(true, overlappingDeadlinesData, "post", "json", true);

        if (overlappingDeadlines.data.length > 0) {
            setConflictsMessage(`${overlappingDeadlines.data.join("\n")}\n\n${t("Vuoi continuare comunque?")}`);
        }

        const overlappingDeadlinesRoundChecked = localStorage.getItem("overlappingDeadlinesRound");

        if (overlappingDeadlines.data.length > 0 && overlappingDeadlinesRoundChecked !== "bypass") {
            setPendingFormData(dataCopy);

            setOverlappingModal(true);
            setConflictsMessage(`${overlappingDeadlines.data.join("\n")}\n\n${t("Vuoi continuare comunque?")}`);
            localStorage.setItem("overlappingDeadlinesRound", "checked");
            return;
        }

        localStorage.removeItem("overlappingDeadlinesRound");

        await usePostRequest.doFetch(true, formData);
        navigate(locationState.prevPath, {
            state: {
                defaultParams: locationState.defaultParams,
                fileHeaderData: locationState.fileHeaderData
            }
        });
    };

    const stripHTML = (html: string): string => {
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = html;
        return tempDiv.textContent || tempDiv.innerText || "";
    };

    const handleOverlapingModalConfirm = () => {
        localStorage.setItem("overlappingDeadlinesRound", "bypass");
        setOverlappingModal(false);
        if (pendingFormData) {
            onSubmit(pendingFormData);
            setPendingFormData(null);
        }
    };

    const handleOverlapingModalCancel = () => {
        localStorage.removeItem("overlappingDeadlinesRound");
        setOverlappingModal(false);
        setPendingFormData(null);
    };

    const itemsData = locationState.prevPath === "/calendar/calendar" ? items : locationState.items;

    return (
        <VaporPage>
            <PageTitle title={Object.keys(rowData.form).length === 0 ? t("Seleziona Pratica") : stripHTML(rowData.form.agendaHeader)} pathToPrevPage={location.state.prevPath ?? "/agenda/agenda"} stateOptions={{ defaultParams: location.state.defaultParams, fileHeaderData: location.state.fileHeaderData }} showBackButton={true} />

            <ConfirmModal open={overlappingModal} handleDecline={() => handleOverlapingModalCancel()} handleAgree={() => handleOverlapingModalConfirm()} decline={t("Annulla")} agree={t("Conferma")} title={t("Conflitti Attività:")}>
                <TableContainer>
                    <TableBody>
                        {conflictsMessage.split("\n").map((row: string, index: number) => (
                            <TableRow key={index}>{row}</TableRow>
                        ))}
                    </TableBody>
                </TableContainer>
            </ConfirmModal>
            <VaporPage.Section>
                <AgendaUpdateProvider>
                    <Box sx={{ "& > *": { my: 2 } }}>
                        {!showAuthorityForm && Object.keys(rowData.form).length === 0 && (
                            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                                <CustomAutocomplete
                                    options={practicaArchive}
                                    loadingText={t("Caricamento…")}
                                    noOptionsText={t("Nessuna opzione")}
                                    isOptionEqualToValue={(option: any, value: any) => option.value === value.value}
                                    renderInput={(params: any) => (
                                        <TextField
                                            {...params}
                                            placeholder={t("Cerca pratica per codice, descrizione, nominativi, RG…")}
                                            InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                    <>
                                                        {isLoading ? <CircularProgress size={20} /> : null}
                                                        {params.InputProps.endAdornment}
                                                    </>
                                                )
                                            }}
                                        />
                                    )}
                                    onInputChange={(_event: any, value: any, reason: string) => {
                                        if (reason === "input") {
                                            setSearch(value);
                                        }
                                    }}
                                    value={selectedPractica}
                                    onChange={(_event: any, item: any) => {
                                        handlePraticaChange(item);
                                    }}
                                />
                            </Box>
                        )}

                        {showAuthorityForm && (
                            <Authority
                                selectedFile={selectedPractica}
                                hideAuthority={(id: string) => {
                                    setShowAuthorityForm(false);
                                    setShowFullForm(true);
                                    setSelectedAfterAuthority(id);
                                }}
                            />
                        )}

                        {showFullForm && (
                            <>
                                <Box>
                                    <Tabs value={value} onChange={handleChange} size="extraSmall" variant="standard">
                                        <Tab label={t("Scheda Udienza")} {...a11yProps(0)} />
                                        <Tab label={t("  Dati aggiuntivi")} {...a11yProps(1)} />
                                    </Tabs>
                                </Box>
                                <CustomTabPanel value={value} index={0}>
                                    <Box sx={{ p: 1 }}>
                                        <SchedaUdienzaTab rowData={rowData} method={method} onSubmit={onSubmit} saveLoading={usePostRequest.loading} setValidation={setValidation} prevPath={location.state.prevPath} rowDataUrl={locationState.rowDataUrl} items={itemsData} defaultParams={locationState.defaultParams} fileHeaderData={locationState.fileHeaderData} />
                                    </Box>
                                </CustomTabPanel>
                                <CustomTabPanel value={value} index={1}>
                                    <Box sx={{ p: 1 }}>
                                        <DateAggiuntivi items={itemsData} rowData={rowData} />
                                    </Box>
                                </CustomTabPanel>
                            </>
                        )}
                    </Box>
                </AgendaUpdateProvider>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default CreateUdienza;
