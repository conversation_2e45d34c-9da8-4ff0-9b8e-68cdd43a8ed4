export interface IAnagraficaParams {
    uniqueid?: string;
    id?: string;
    fileReference?: string;
    tipo?: string;
    nome?: string;
    titolo?: string;
    subjectSurname?: string;
    subjectName?: string;
    subjectGender?: string;
    luogonascita?: string;
    datanascita?: string;
    codicefiscale?: string;
    partitaiva?: string;
    contact?: string;
    categoria?: string;
    codiceb2b?: string;
    lawyer<PERSON><PERSON>?: string;
    annotazioni?: string;
    rapid_ext_access?: string;
    spese_generali?: string;
    perc_ritenuta?: string;
    tipo_ritenuta?: string;
    tipo_esenzione?: string;
    modalita_pagamento?: string;
    note_pagamento?: string;
    iban?: string;
    banca?: string;
    codiceb2bSearch?: string;
    listino?: string;
    listino_orario?: string;
    tariffa_oraria?: string;
    formula?: string;
    tipo_identificazione?: string;
    tipo_documento?: string;
    num_documento?: string;
    data_rilascio?: string;
    autorita_rilascio?: string;
    autorita_comune?: string;
    autorita_provincia?: string;
    professione?: string;
    comune?: string;
    sezione?: string;
    dataiscrizione?: string;
    nrea?: string;
    deliberato?: string;
    sottoscritto?: string;
    versato?: string;
    valuta?: string;
    pctid?: string;
    provincia?: string;
    split_payment?: string;
    iva_esente?: string | null;
    rit_acconto?: string | null;
    esente_cassa?: string | null;
    spesefatturabili?: string | null;
    orefatturabili?: string | null;
    aui?: {
        autorita_comune?: string;
        autorita_provincia?: string;
        autorita_rilascio?: string;
        data_rilascio?: string;
        num_documento?: string;
        professione?: string;
        tipo_documento?: string;
        tipo_identificazione?: string;
    };
    addresses?: {};
    scissione_pagamenti: string;
    denominazione_val: string;
    [key: string]: any;
}

export const DEFAULT_ANAGRAFICHE_PARAMS: IAnagraficaParams = {
    uniqueid: "",
    id: "",
    fileReference: "",
    tipo: "",
    nome: "",
    titolo: "-1",
    codiceb2bSearch: "",
    subjectSurname: "",
    subjectName: "",
    subjectGender: "M",
    luogonascita: "",
    datanascita: "",
    codicefiscale: "",
    partitaiva: "",
    contact: "",
    categoria: "-1",
    codiceb2b: "",
    lawyerRole: "0",
    annotazioni: "",
    rapid_ext_access: "on",
    spese_generali: "",
    perc_ritenuta: "",
    tipo_ritenuta: "RT01",
    tipo_esenzione: "N1",
    modalita_pagamento: "MP01",
    note_pagamento: "",
    iban: "",
    banca: "0",
    listino: "0",
    listino_orario: "0",
    tariffa_oraria: "",
    tipo_identificazione: "1",
    tipo_documento: "-1",
    num_documento: "",
    data_rilascio: "",
    autorita_rilascio: "-1",
    autorita_comune: "-1",
    autorita_provincia: "-1",
    professione: "",
    comune: "0",
    sezione: "",
    dataiscrizione: "",
    nrea: "",
    deliberato: "",
    sottoscritto: "",
    versato: "",
    valuta: "0",
    pctid: "",
    provincia: "",
    split_payment: "",
    iva_esente: null,
    rit_acconto: null,
    esente_cassa: null,
    spesefatturabili: null,
    orefatturabili: null,
    formula_saluti: "",
    scissione_pagamenti: "",
    denominazione_val: "",
};
