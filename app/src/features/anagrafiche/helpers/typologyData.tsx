import {
    faCarBuilding,
    faUserTie,
    faBuilding,
    faUser,
    faLandmark,
    faCity,
    faUsers,
    faHandHoldingHeart,
    faUserPlus,
} from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export const useCardData = (anagraficheData: any) => {
    const { groupTypologies } = anagraficheData;

    const iconMapping: any = {
        "Ditta individuale": faCarBuilding,
        "Persona fisica": faUserTie,
        Società: faBuilding,
        Professionista: faUser,
        "Ente pubblico": faLandmark,
        Condominio: faCity,
        "Ente sociale": faHandHoldingHeart,
        Associazione: faUsers,
        Altro: faUserPlus,
        "Non specificata": faUserPlus,
    };

    function capitalizeFirstLetter(val: string) {
        return String(val).charAt(0).toUpperCase() + String(val).slice(1);
    }

    // Generate cardData from groupTypologies
    const cardData = (groupTypologies || []).flatMap((group: any) => {
        return {
            id: group.id,
            title: capitalizeFirstLetter(group.name),
            description: group.description,
            details: group.typologies,
            defaultIdOption: group.defaultIdOption,
            icon: (
                <FontAwesomeIcon
                    icon={iconMapping[group.name] || faUserPlus} // Default icon if none matches
                    style={{ marginLeft: "5px", color: "#1e88e5" }}
                />
            ),
        };
    });

    return cardData;
};
