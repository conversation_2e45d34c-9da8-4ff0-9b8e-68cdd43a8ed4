import React from "react";
import { useSearchParams } from "react-router-dom";
import { Vapor<PERSON><PERSON>, Toolbar } from "@vapor/react-custom";
import { Tab, Tabs } from "@vapor/react-extended";
import { Box, Drawer, Stack } from "@vapor/react-material";
import Button from "@mui/material/Button";
import { FormProvider } from "react-hook-form";
import { Title } from "@vapor/react-custom";
import ToastNotification from "../../../../../custom-components/ToastNotification";
import {
    a11yProps,
    CustomTabPanel,
} from "../../../../../helpers/customTabPanel";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import ImmobileTab from "./ImmobileTab";
import { useImmobileUpdateHooks } from "../../../hooks/useImmobileUpdateHook";
import <PERSON><PERSON>itaTab from "./TitolaritaTab";
import CatastaliTab from "./CatastaliTab";
import PregiudizievoliTab from "./PregiudizievoliTab";
import usePostCustom from "../../../../../hooks/usePostCustom";
import moment from "moment";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { useAnagraficheProvider } from "../../../providers/AnagraficheProvider";
interface PropertyRight {
    value: number | string;
    label: string;
}

interface Person {
    value: number | string;
    label: string;
}
const CreateUpdateImmobili = (props: any) => {
    const { openDrawer, setOpenDrawer, idSelected, setIdSelected, fetchData } =
        props;
    const [value, setValue] = React.useState(0);
    const [showDuplicateError, setShowDuplicateError] =
        React.useState<boolean>(false);
    const [propertyRights, setPropertyRights] = React.useState<PropertyRight[]>(
        []
    );
    const [personsList, setPersonsList] = React.useState<Person[]>([]);

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };
    const { method }: any = useImmobileUpdateHooks(idSelected);
    const { handleSubmit, reset, setValue: setFormValue, watch } = method;
    const { anagrafiche } = useAnagraficheProvider();

    const propertyRightsRequest = useGetCustom(
        "peopleproperties/getpropertyrights?noTemplateVars=true"
    );

    const personsRequest = useGetCustom(
        "/peopleproperties/getpersonslist?noTemplateVars=true"
    );

    const updateRequest = usePostCustom(
        "peopleproperties/editproperty?noTemplateVars=true"
    );

    const addRequest = usePostCustom(
        "peopleproperties/addproperty?noTemplateVars=true"
    );
    const [params] = useSearchParams();
    const anag_id: string = params.get("id") as string;

    const { t } = useTranslation();

    const closeDrawer = () => {
        setOpenDrawer(false);
        setIdSelected("");
        reset();
    };

    React.useEffect(() => {
        reset();
    }, [idSelected, reset]);

    React.useEffect(() => {
        const getInitialData = async () => {
            propertyRightsRequest.doFetch(true).then((response: any) => {
                setPropertyRights(
                    response.data.map((item: any) => ({
                        value: item.id,
                        label: item.nome,
                    }))
                );
            });

            personsRequest.doFetch(true).then((response: any) => {
                setPersonsList(
                    response.data.map((item: any) => ({
                        value: item.id,
                        label: item.denominazione,
                    }))
                );
            });
        };

        getInitialData();
    }, []);

    React.useEffect(() => {
        if (idSelected === "create") {
            setFormValue("anagrafica.0.anag_id", anag_id);
            setFormValue("anagrafica.0.nome_id", anag_id);

            const foundPerson = personsList.find(
                (item) => item.value === anag_id
            );
            setFormValue(
                "anagrafica.0.nome",
                foundPerson?.label ?? anag_id.toString()
            );
            setFormValue("anagrafica.0.quota", "0.00");
            setFormValue("anagrafica.0.pignorato", 1);
            if (propertyRights?.length > 0) {
                setFormValue("anagrafica.0.diritto", propertyRights[0].value);
            }
        }
    }, [propertyRights, personsList, setValue, idSelected, anag_id]);

    const cittaSelected = watch("citta");

    React.useEffect(() => {
        if (cittaSelected) {
            const getProvincia = anagrafiche?.cities?.find(
                (city: any) => city.id === cittaSelected
            )?.provincia;

            if (watch("prov") !== getProvincia) {
                setFormValue("prov", getProvincia);
            }
        }
    }, [cittaSelected]);

    const transformDataForSubmission = (anagraficaData: any[]) => {
        const transformedData: Record<string, any> = {};

        anagraficaData.forEach((item, index) => {
            const oneBasedIndex = index + 1;

            if (item.id)
                transformedData[`anagrafica[${oneBasedIndex}][id]`] = item.id;
            transformedData[`anagrafica[${oneBasedIndex}][nome_id]`] =
                item.nome_id || item.anag_id;
            if (item.nome)
                transformedData[`anagrafica[${oneBasedIndex}][nome]`] =
                    item.nome;
            transformedData[`anagrafica[${oneBasedIndex}][diritto]`] =
                item.diritto;
            transformedData[`anagrafica[${oneBasedIndex}][quota]`] = item.quota;
            transformedData[`anagrafica[${oneBasedIndex}][pignorato]`] =
                item.pignorato ? 1 : "";
        });

        return transformedData;
    };

    const onSubmit = async (data: any) => {
        const { anagrafica, daticatastali, pregiud, ...restData } = data;
        const formData = new FormData();

        Object.keys(restData).forEach((key: string) => {
            if (key === "classato") {
                formData.append(
                    key,
                    restData[key] === "1" || restData[key] === "true"
                        ? "1"
                        : "0"
                );
            } else {
                formData.append(key, restData[key]);
            }
        });

        daticatastali.forEach((item: any, index: number) => {
            const formIndex = index + 1;
            Object.entries(item).forEach(([key, value]) => {
                formData.append(
                    `daticatastali[${formIndex}][${key}]`,
                    value ? (value as string) : ""
                );
            });
        });

        pregiud.forEach((item: any, index: number) => {
            const formIndex = index + 1;
            Object.entries(item).forEach(([key, value]) => {
                if (key === "data_pregiud") {
                    formData.append(
                        `pregiud[${formIndex}][data]`,
                        moment(value as string).isValid()
                            ? (moment(value as string).format(
                                  "DD/MM/YYYY"
                              ) as string)
                            : ""
                    );
                } else {
                    formData.append(
                        `pregiud[${formIndex}][${key}]`,
                        value ? (value as string) : ""
                    );
                }
            });
        });
        const transformedData = transformDataForSubmission(anagrafica);
        Object.entries(transformedData).forEach(([key, value]) => {
            formData.append(key, value ? (value as string) : "");
        });

        if (idSelected === "create") {
            formData.delete("id");
            if (anagrafica.length > 0) {
                formData.append(`anagrafica[1][first_person]`, "true");
                formData.append(`anag_id`, anag_id);
            }

            const response: any = await addRequest.doFetch(true, formData);

            if (response.data === 2) {
                setShowDuplicateError(true);
                // setOpenDrawer(false);
            } else if (response.data) {
                setOpenDrawer(false);
                fetchData();
            }
        } else {
            const response: any = await updateRequest.doFetch(true, formData);
            if (response.data) {
                setOpenDrawer(false);
                fetchData();
            }
        }
    };

    return (
        <>
            <ToastNotification
                showNotification={showDuplicateError}
                setShowNotification={setShowDuplicateError}
                severity="error"
                text={t(
                    "Non è possibile inserire un immobile con lo stesso nome di uno già esistente"
                )}
            />
            <Drawer
                anchor="right"
                open={openDrawer}
                onClose={setOpenDrawer}
                hideBackdrop
                width={1000}
            >
                <div
                    style={{
                        width: 990,
                        position: "sticky",
                    }}
                >
                    <Title
                        divider
                        reduced
                        rightItems={[
                            <i color="primary" onClick={closeDrawer}>
                                <Close />
                            </i>,
                        ]}
                        size="medium"
                        title={t("NUOVO IMMOBILE")}
                    />
                </div>
                <FormProvider {...method}>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <VaporPage
                            contentToolbar={
                                <Toolbar
                                    contentRight={
                                        <Stack
                                            direction="row"
                                            gap={1}
                                            paddingLeft={2}
                                        >
                                            <Button
                                                variant="contained"
                                                type="submit"
                                                sx={{
                                                    ml: 1,
                                                }}
                                            >
                                                {t("Conferma")}
                                            </Button>
                                        </Stack>
                                    }
                                    contentLeft={
                                        <Stack
                                            direction="row"
                                            gap={1}
                                            paddingLeft={2}
                                        >
                                            <Button
                                                onClick={() => closeDrawer()}
                                            >
                                                {t("Chiudi")}
                                            </Button>
                                        </Stack>
                                    }
                                />
                            }
                        >
                            <Box>
                                <Tabs
                                    value={value}
                                    onChange={handleChange}
                                    size="small"
                                    variant="standard"
                                >
                                    <Tab label="Immobile" {...a11yProps(0)} />
                                    <Tab label="Titolarità" {...a11yProps(1)} />
                                    <Tab label="Catastali" {...a11yProps(2)} />
                                    <Tab
                                        label="Pregiudizievoli"
                                        {...a11yProps(3)}
                                    />
                                </Tabs>
                            </Box>
                            <CustomTabPanel value={value} index={0}>
                                <ImmobileTab />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={1}>
                                <TitolaritaTab
                                    idSelected={idSelected}
                                    anag_id={anag_id}
                                    propertyRights={propertyRights}
                                    personsList={personsList}
                                />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={2}>
                                <CatastaliTab />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={3}>
                                <PregiudizievoliTab />
                            </CustomTabPanel>
                        </VaporPage>
                    </form>
                </FormProvider>
            </Drawer>
        </>
    );
};

export default CreateUpdateImmobili;
