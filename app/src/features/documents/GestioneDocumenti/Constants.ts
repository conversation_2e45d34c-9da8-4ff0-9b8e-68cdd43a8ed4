import { DocumentListFilters } from "../../../interfaces/documents.interface";

export const VISIBILITY_OPTIONS = [
    { value: "-1", label: "A tutti" },
    { value: "0", label: "Solo interni" },
    { value: "1", label: "Solo esterni" },
];

export const SIGNATURE_STATUS = [
    { value: "-1", label: "Tutti" },
    { value: "0", label: "Non in firma" },
    { value: "1", label: "In bozza" },
    { value: "2", label: "Pronto per l'invio" },
    { value: "3", label: "Caricato" },
    { value: "4", label: "In elaborazione" },
    { value: "5", label: "In firma" },
    { value: "6", label: "Parzialmente firmato" },
    { value: "7", label: "Firmato" },
    { value: "8", label: "Eliminato" },
    { value: "9", label: "Rifiutato" },
];

export const PRACTICE_SUBJECT_ROLES = [
    { value: "1", label: "Cliente" },
    { value: "2", label: "Controparte" },
    { value: "3", label: "Avversario" },
    { value: "4", label: "Altro" },
    { value: "5", label: "Dominus" },
    { value: "6", label: "Procuratore" },
    { value: "7", label: "Esterno" },
    { value: "8", label: "Professionista esterno" },
    { value: "20", label: "Cointestatario" },
    { value: "21", label: "Domiciliatario" },
    { value: "22", label: "Corrispondente" },
    { value: "23", label: "Cliente Fatturazione" },
];

export const PRACTICE_DESCRIPTION = [
    { value: "codicearchivio", label: "Codice archivio" },
    { value: "codicepratica", label: "Codice pratica" },
    { value: "descrizionepratica", label: "Descrizione pratica" },
    { value: "oggettopratica", label: "Oggetto" },
    { value: "controparte", label: "Controparte" },
    { value: "cliente", label: "Cliente" },
    { value: "RG", label: "RG" },
];

export const DOCUMENT_TYPES = [
    { label: "Documento di word", value: 0 },
    { label: "Cartella di lavoro di Excel", value: 1 },
    { label: "Presentazione di PowerPoint", value: 2 },
];

export const SEARCH_PARAMS: DocumentListFilters = {
    titolodocumento: "",
    documentoDi: "",
    endDate: "",
    fileSearch: "",
    fileUniqueid: "",
    note: "",
    searchCategories: "-1",
    startDate: "",
    visibility: "-1",
    searchField: "",
    noupdate: false,
    archiveSubject: "",
    archiveSubjectRelation: "",
    contractSearch: "",
    contractUuid: "",
    protocollo: "",
    signatureStatus: "",
    search: true,
};
