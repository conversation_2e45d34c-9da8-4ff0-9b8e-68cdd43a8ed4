import { useAddDocumentsStatus } from "./hooks/DocumentsStatus";
import { TextField, FormControl, ListItem } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { useEffect, useState } from "react";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";

interface Status {
    nome: string;
    id: string;
}

export const DocumentStatusSearch = ({ documentStatus, handleUpdate, setDocumentStatus }: { documentStatus: Status[]; handleUpdate: any; setDocumentStatus: any }) => {
    const { t } = useTranslation();

    const [value, setValue] = useState<Status>({
        nome: "",
        id: ""
    });

    const [addStatus, setAddStatus] = useState(false);

    const { loading, hasLoaded, data } = useAddDocumentsStatus({
        insertItem: value.nome,
        addItem: addStatus
    });

    useEffect(() => {
        if (!loading && hasLoaded) {
            setAddStatus(false);
            setValue({ nome: data.description, id: data.id });
        }
    }, [loading, hasLoaded]);

    const handleChange = (_event: React.SyntheticEvent, newValue: any) => {
        if (typeof newValue === "string") {
            setValue({
                nome: newValue,
                id: ""
            });
        } else if (newValue && newValue.inputValue) {
            setValue({
                nome: newValue.inputValue,
                id: ""
            });
            setAddStatus(true);
        } else {
            setValue(newValue);
        }
        handleUpdate("mittente")(newValue.id);
        setDocumentStatus([
            ...documentStatus,
            {
                nome: newValue.nome.replace(/^Add\s*"(.+)"$/, "$1"),
                id: newValue.id
            }
        ]);
    };

    return (
        <FormControl>
            <CustomAutocomplete
                value={value}
                onChange={handleChange}
                filterOptions={(options: any, params: any) => {
                    const { inputValue } = params;

                    const filtered = options.filter((option: any) => option.nome.includes(inputValue));

                    const isExisting = filtered.some((option: any) => option.nome.toLowerCase() === inputValue.toLowerCase());

                    if (inputValue !== "" && !isExisting) {
                        filtered.push({
                            inputValue,
                            nome: `${t("Add")} "${inputValue}"`,
                            id: ""
                        });
                    }

                    return filtered;
                }}
                selectOnFocus
                clearOnBlur
                handleHomeEndKeys
                options={documentStatus}
                getOptionLabel={(option: any) => {
                    if (typeof option === "string") {
                        return option;
                    }
                    if (option.inputValue) {
                        return option.inputValue;
                    }
                    return option.nome;
                }}
                renderOption={(props: any, option: Status) => (
                    <ListItem {...props} value={option.id}>
                        {option.nome}
                    </ListItem>
                )}
                sx={{ width: 300 }}
                renderInput={(params: any) => <TextField {...params} label={t("Stato")} />}
            />
        </FormControl>
    );
};
